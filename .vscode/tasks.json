{"version": "2.0.0", "tasks": [{"type": "npm", "script": "dev", "path": "ruoyi-ui", "problemMatcher": [], "label": "npm: dev - ruoyi-ui", "detail": "vue-cli-service serve", "group": {"kind": "build", "isDefault": true}, "runOptions": {"runOn": "folderOpen"}}, {"label": "Run Spring Boot (Java)", "type": "shell", "command": "mvn spring-boot:run", "args": ["compile", "exec:java", "mainClass=com.ruoyi.RuoYiApplication"], "group": "build", "problemMatcher": [], "options": {"cwd": "${workspaceFolder}/ruoyi-admin"}}, {"label": "启动SpringBoot应用", "type": "shell", "command": "cd ruoyi-admin ; mvn spring-boot:run", "group": {"kind": "test", "isDefault": true}, "runOptions": {"runOn": "folderOpen"}, "presentation": {"reveal": "never", "clear": true, "panel": "dedicated", "close": false}}, {"label": "启动RuoYiApplication", "type": "shell", "options": {"cwd": "${workspaceFolder}/ruoyi-admin"}, "command": "\"D:\\localGreenSoft\\maven\\apache-maven-3.8.8\\bin\\mvn.cmd\" exec:java -Dexec.mainClass=com.ruoyi.RuoYiApplication", "group": {"kind": "test"}, "presentation": {"reveal": "always", "clear": true, "panel": "dedicated", "close": false}}, {"label": "mvn clean install", "type": "shell", "command": "mvn clean install", "group": {"kind": "build", "isDefault": true}, "presentation": {"clear": true, "panel": "dedicated", "close": false}}, {"label": "mvn clean package", "type": "shell", "command": "mvn clean package", "group": {"kind": "build", "isDefault": true}, "presentation": {"clear": true, "panel": "dedicated", "close": false}}]}