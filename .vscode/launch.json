{"version": "0.2.0", "configurations": [{"name": "前端: npm dev (RuoYi-UI)", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "cwd": "${workspaceFolder}/ruoyi-ui"}, {"name": "后端: Spring Boot", "type": "java", "request": "launch", "mainClass": "com.ruoyi.RuoYiApplication", "projectName": "ruoyi-admin"}, {"name": "测试: Run Jest", "type": "node", "request": "launch", "program": "${workspaceFolder}/ruoyi-ui/node_modules/jest/bin/jest.js", "args": ["--watch<PERSON>ll"]}]}