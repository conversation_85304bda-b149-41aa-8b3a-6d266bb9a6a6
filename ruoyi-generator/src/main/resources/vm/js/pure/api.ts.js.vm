import { http } from "@/utils/http";

const prefix = "/${moduleName}/${businessName}";

type Result = {
  code: number;
  msg: string;
  data?: any;
  total?: number;
};

/**
 * ${functionName}列表
 * @param data 查询参数
 * @returns
 */
export const list${BusinessName} = (data?: object) => {
  return http.request<Result>("post", `${prefix}/list`, {
    data,
    headers: { "Content-Type": "multipart/form-data" }
  });
};

/**
 * ${functionName}详情
 * @param ${pkColumn.javaField} ${functionName}ID
 * @returns
 */
export const info${BusinessName} = (${pkColumn.javaField}?: string) => {
  return http.request<Result>("post", `${prefix}/info/${${pkColumn.javaField}}`);
};

/**
 * 新增${functionName}
 * @param data 新增的数据
 * @returns
 */
export function add${BusinessName}(data) {
  return http.request<Result>("post", `${prefix}/add`, { data });
}

/**
 * 修改${functionName}
 * @param data 修改的数据
 * @returns
 */
export function edit${BusinessName}(data) {
  return http.request<Result>("post", `${prefix}/edit`, { data });
}

/**
 * 删除${functionName}
 * @param ${pkColumn.javaField} ${functionName}ID
 * @returns
 */
export function remove${BusinessName}(${pkColumn.javaField}) {
  return http.request<Result>("post", `${prefix}/remove/${${pkColumn.javaField}}`);
}
