<script setup lang="ts">
  import {reactive, ref} from "vue";
  import {FormProps} from "../utils/types";
  import {extend} from "../utils/extend";

  const props = defineProps<FormProps>();

  const formRef = ref();
  const form = reactive(props.formInline);
  const options = reactive({ ...props.options });
  const pageType = ref(props.pageType);
  const emit = defineEmits(["close"]);

  function getRef() {
    return formRef.value;
  }
  defineExpose({ getRef });

  const { changeRoot, changeIsp, changeCus } = extend(form, options);
</script>

<template>
  <el-form
      ref="formRef"
      :class="pageType"
      :model="form"
      :rules="formRules"
      label-width="120px"
  >
    <el-row :gutter="10">
#foreach($column in $columns)
    #set($field=$column.javaField)
    #if($column.insert && !$column.pk)
        #if(($column.usableColumn) || (!$column.superColumn))
            #set($parentheseIndex=$column.columnComment.indexOf("("))
            #if($parentheseIndex != -1)
                #set($comment=$column.columnComment.substring(0, $parentheseIndex))
            #else
                #set($comment=$column.columnComment)
            #end
            #set($dictType=$column.dictType)
          <el-col :span="24">
          #if($column.htmlType == "input")
            <el-form-item label="${comment}" prop="${field}">
              <el-input v-model="form.${field}" disabled/>
            </el-form-item>
          #elseif($column.htmlType == "imageUpload")
            <el-form-item label="${comment}" prop="${field}">
              <image-upload v-model="form.${field}" disabled/>
            </el-form-item>
          #elseif($column.htmlType == "fileUpload")
            <el-form-item label="${comment}" prop="${field}">
              <file-upload v-model="form.${field}" disabled/>
            </el-form-item>
          #elseif($column.htmlType == "editor")
            <el-form-item label="${comment}">
              <editor v-model="form.${field}" :min-height="192"/>
            </el-form-item>
          #elseif($column.htmlType == "select" && "" != $dictType)
            <el-form-item label="${comment}" prop="${field}">
              <el-select v-model="form.${field}" placeholder="请选择" disabled>
                <el-option
                    v-for="dict in dict.type.${dictType}"
                    :key="dict.value"
                    :label="dict.label"
                    #if($column.javaType == "Integer" || $column.javaType == "Long")
                    :value="parseInt(dict.value)"
                    #else
                    :value="dict.value"
                    #end
                ></el-option>
              </el-select>
            </el-form-item>
          #elseif($column.htmlType == "select" && $dictType)
            <el-form-item label="${comment}" prop="${field}">
              <el-select v-model="form.${field}" placeholder="请选择" disabled>
                <el-option label="请选择字典生成" value="" />
              </el-select>
            </el-form-item>
          #elseif($column.htmlType == "checkbox" && "" != $dictType)
            <el-form-item label="${comment}" prop="${field}">
              <el-checkbox-group v-model="form.${field}">
                <el-checkbox
                    v-for="dict in dict.type.${dictType}"
                    :key="dict.value"
                    :label="dict.value">
                  {{dict.label}}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          #elseif($column.htmlType == "checkbox" && $dictType)
            <el-form-item label="${comment}" prop="${field}">
              <el-checkbox-group v-model="form.${field}" disabled>
                <el-checkbox>请选择字典生成</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          #elseif($column.htmlType == "radio" && "" != $dictType)
            <el-form-item label="${comment}" prop="${field}">
              <el-radio-group v-model="form.${field}" disabled>
                <el-radio
                    v-for="dict in dict.type.${dictType}"
                    :key="dict.value"
                    #if($column.javaType == "Integer" || $column.javaType == "Long")
                    :label="parseInt(dict.value)"
                    #else
                    :label="dict.value"
                    #end
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          #elseif($column.htmlType == "radio" && $dictType)
            <el-form-item label="${comment}" prop="${field}">
              <el-radio-group v-model="form.${field}" disabled>
                <el-radio label="1">请选择字典生成</el-radio>
              </el-radio-group>
            </el-form-item>
          #elseif($column.htmlType == "datetime")
            <el-form-item label="${comment}" prop="${field}">
              <el-date-picker clearable
                              v-model="form.${field}"
                              type="date"
                              value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          #elseif($column.htmlType == "textarea")
            <el-form-item label="${comment}" prop="${field}">
              <el-input v-model="form.${field}" type="textarea" disabled/>
            </el-form-item>
          #end
          </el-col>

        #end
    #end
#end
    </el-row>
  </el-form>
</template>
