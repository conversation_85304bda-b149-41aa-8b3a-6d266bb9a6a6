<script setup lang="ts">
import { reactive, ref } from "vue";
import { extend } from "../utils/extend";
import { formRules } from "../utils/rule";
import { FormProps } from "../utils/types";

const props = defineProps<FormProps>();
const formRef = ref();
const form = reactive(props.formInline);
const options = reactive({ ...props.options });
const pageType = ref(props.pageType);
const emit = defineEmits(["close"]);

const { initData } = extend(form, options);
initData();

if (["add"].includes(pageType.value)) {
  #foreach($column in $columns)
    #set($javaField=$column.javaField)
    #set($parentheseIndex=$column.columnComment.indexOf("("))
    #if($parentheseIndex != -1)
      #set($comment=$column.columnComment.substring(0, $parentheseIndex))
    #else
      #set($comment=$column.columnComment)
    #end
  form.${javaField} ||= null; // ${comment}
  #end
}
const getRef = () => formRef.value;
defineExpose({ getRef });
</script>

<template>
  <el-form
    ref="formRef"
    :class="pageType"
    :model="form"
    :rules="['view'].includes(pageType) ? null : formRules"
    :disabled="['view'].includes(pageType)"
    :show-message="false"
    :status-icon="true"
    label-width="auto"
  >
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="服务商" prop="fIspID">
          <el-select
            v-model="form.fIspID"
            filterable
            :disabled="!options.ispList?.length"
          >
            <el-option
              v-for="item in options.ispList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="客户" prop="fCusID">
          <el-select
            v-model="form.fCusID"
            filterable
            :disabled="!options.cusList?.length"
          >
            <el-option
              v-for="item in options.cusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>

#foreach($column in $columns)
  #set($field=$column.javaField)
  #if($column.insert && !$column.pk)
    #if(($column.usableColumn) || (!$column.superColumn))
      #set($parentheseIndex=$column.columnComment.indexOf("("))
      #if($parentheseIndex != -1)
        #set($comment=$column.columnComment.substring(0, $parentheseIndex))
      #else
        #set($comment=$column.columnComment)
      #end
      #set($dictType=$column.dictType)
      <el-col :span="12">
      #if($column.htmlType == "input")
        <el-form-item label="${comment}" prop="${field}">
          <el-input v-model="form.${field}" />
        </el-form-item>
      #elseif($column.htmlType == "imageUpload")
        <el-form-item label="${comment}" prop="${field}">
          <image-upload v-model="form.${field}" />
        </el-form-item>
      #elseif($column.htmlType == "fileUpload")
        <el-form-item label="${comment}" prop="${field}">
          <file-upload v-model="form.${field}" />
        </el-form-item>
      #elseif($column.htmlType == "editor")
        <el-form-item label="${comment}">
          <editor v-model="form.${field}" :min-height="192" />
        </el-form-item>
      #elseif($column.htmlType == "select" && "" != $dictType)
        <el-form-item label="${comment}" prop="${field}">
          <el-select v-model="form.${field}" placeholder="请选择">
            <el-option
                    v-for="dict in dict.type.${dictType}"
                    :key="dict.value"
                    :label="dict.label"
              #if($column.javaType == "Integer" || $column.javaType == "Long")
                    :value="parseInt(dict.value)"
              #else
                    :value="dict.value"
              #end
            ></el-option>
          </el-select>
        </el-form-item>
      #elseif($column.htmlType == "select" && $dictType)
        <el-form-item label="${comment}" prop="${field}">
          <el-select v-model="form.${field}" placeholder="请选择">
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
      #elseif($column.htmlType == "checkbox" && "" != $dictType)
        <el-form-item label="${comment}" prop="${field}">
          <el-checkbox-group v-model="form.${field}">
            <el-checkbox
                    v-for="dict in dict.type.${dictType}"
                    :key="dict.value"
                    :label="dict.value">
              {{dict.label}}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      #elseif($column.htmlType == "checkbox" && $dictType)
        <el-form-item label="${comment}" prop="${field}">
          <el-checkbox-group v-model="form.${field}">
            <el-checkbox>请选择字典生成</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      #elseif($column.htmlType == "radio" && "" != $dictType)
        <el-form-item label="${comment}" prop="${field}">
          <el-radio-group v-model="form.${field}">
            <el-radio
                    v-for="dict in dict.type.${dictType}"
                    :key="dict.value"
              #if($column.javaType == "Integer" || $column.javaType == "Long")
                    :label="parseInt(dict.value)"
              #else
                    :label="dict.value"
              #end
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      #elseif($column.htmlType == "radio" && $dictType)
        <el-form-item label="${comment}" prop="${field}">
          <el-radio-group v-model="form.${field}">
            <el-radio label="1">请选择字典生成</el-radio>
          </el-radio-group>
        </el-form-item>
      #elseif($column.htmlType == "datetime")
        <el-form-item label="${comment}" prop="${field}">
          <el-date-picker clearable
                          v-model="form.${field}"
                          type="date"
                          value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
      #elseif($column.htmlType == "textarea")
        <el-form-item label="${comment}" prop="${field}">
          <el-input v-model="form.${field}" type="textarea" />
        </el-form-item>
      #end
      </el-col>

    #end
  #end
#end
    </el-row>
  </el-form>
</template>
