import {
  list${BusinessName},
  info${BusinessName},
  add${BusinessName},
  edit${BusinessName},
  remove${BusinessName}
} from "@/api/${moduleName}/${businessName}";
import { addDialog } from "@/components/ReDialog";
import { FormValidator } from "@/utils/formValidator";
import { message } from "@/utils/message";
import routeUtils from "@/utils/routeUtils";
import type { PaginationProps } from "@pureadmin/table";
import { deviceDetection } from "@pureadmin/utils";
import { h, onMounted, reactive, ref, toRaw } from "vue";
import MainForm from "../form/form.vue";
import type { FormItemProps, Options, QueryParams } from "./types";

export function hook() {
  const tableId = "${pkColumn.javaField}";
  const name = routeUtils.getTitle();
  const options = reactive({} as Options);
  const formRef = ref();
  const dataList = ref([]);
  const loading = ref(true);
  const queryParams = reactive({} as QueryParams);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });
  const columns: TableColumnList = [
    #foreach($column in $columns)
      #set($javaField=$column.javaField)
      #set($parentheseIndex=$column.columnComment.indexOf("("))
      #if($parentheseIndex != -1)
        #set($comment=$column.columnComment.substring(0, $parentheseIndex))
      #else
        #set($comment=$column.columnComment)
      #end
      #if(!$column.pk)
    { label: "${comment}", prop: "${javaField}" },
      #end
    #end
    {
      label: "操作",
      fixed: "right",
      headerAlign: "center",
      align: "right",
      width: 200,
      slot: "operation"
    }
  ];

  function handleDelete(id: string) {
    remove${BusinessName}(id).then(() => {
      message(`删除成功`, { type: "success" });
      onSearch();
    });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  function handleSelectionChange(val) {
    console.log("handleSelectionChange", val);
  }

  async function onSearch() {
    loading.value = true;
    queryParams.pageSize = pagination.pageSize;
    queryParams.pageNum = pagination.currentPage;
    try {
      const { data, total } = await list${BusinessName}(toRaw(queryParams));
      dataList.value = data;
      pagination.total = total;
    } finally {
      loading.value = false;
    }
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    pagination.currentPage = 1;
    onSearch();
  };

  const handleInfo${BusinessName} = async (id?: string) => {
    if (!id) return {};
    const info = (await info${BusinessName}(id)).data;
    return info;
  };

  async function openDialog(title = "新增", id?: string) {
    const pageTypeList = [
      { type: "view", title: "查看" },
      { type: "add", title: "新增" },
      { type: "edit", title: "修改" }
    ];
    const pageType = pageTypeList.find(item => item.title === title)?.type;
    const formInline = await handleInfo${BusinessName}(id);
    addDialog({
      title: `${title}${name}`,
      props: { formInline },
      width: "750px",
      draggable: true,
      fullscreen: deviceDetection(),
      closeOnClickModal: false,
      hideFooter: pageType === "view",
      contentRenderer: () =>
        h(MainForm, {
          ref: formRef,
          formInline: null,
          options: options,
          pageType: pageType
        }),
      beforeSure: async (done, { options }) => {
        await FormValidator.validate(formRef.value.getRef());
        const curData = { ...options.props.formInline } as FormItemProps;
        if (pageType === "add") {
          await add${BusinessName}(curData);
        } else {
          await edit${BusinessName}(curData);
        }
        message(`${title}成功`, { type: "success" });
        done();
        onSearch();
      }
    });
  }

  /** 高亮当前权限选中行 */
  function rowStyle({ row }) {
    const colorConfig = {};
    return {
      color: colorConfig[row.fStatus] ?? ""
    };
  }

  onMounted(async () => {
    onSearch();
  });

  return {
    tableId,
    queryParams,
    loading,
    columns,
    rowStyle,
    dataList,
    pagination,
    onSearch,
    resetForm,
    openDialog,
    handleDelete,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange
  };
}
