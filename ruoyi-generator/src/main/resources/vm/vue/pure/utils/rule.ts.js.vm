import type { FormRules } from "element-plus";
import { reactive } from "vue";

/** 自定义表单规则校验 */
export const formRules = reactive<FormRules>({
#foreach($column in $columns)
  #set($javaField=$column.javaField)
  #set($parentheseIndex=$column.columnComment.indexOf("("))
  #if($parentheseIndex != -1)
    #set($comment=$column.columnComment.substring(0, $parentheseIndex))
  #else
    #set($comment=$column.columnComment)
  #end
  ${javaField}: [{ required: true, message: "", trigger: "blur" }], // ${comment}
#end
});
