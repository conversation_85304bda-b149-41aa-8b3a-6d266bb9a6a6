export interface FormItemProps {
#foreach($column in $columns)
  #set($javaField=$column.javaField)
  #set($parentheseIndex=$column.columnComment.indexOf("("))
  #if($parentheseIndex != -1)
    #set($comment=$column.columnComment.substring(0, $parentheseIndex))
  #else
    #set($comment=$column.columnComment)
  #end
  /** ${comment} */
  ${javaField}: string;
#end
}

export interface Options {
  ispList: any;
  cusList: any;
}

export interface FormProps {
  formInline: FormItemProps;
  options: Options;
  pageType: string;
}

export type QueryParams = BaseQueryParams & FormItemProps;
