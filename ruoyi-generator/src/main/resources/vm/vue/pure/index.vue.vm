<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { PureTableBar } from "@/components/RePureTableBar";
import { Pers } from "@/utils/commonEnums";
import routeUtils from "@/utils/routeUtils";
import { reactive, ref } from "vue";
import { extend } from "./utils/extend";
import { hook } from "./utils/hook";
import { Options } from "./utils/types";

defineOptions({
  name: "${businessName}"
});

const formRef = ref();
const tableRef = ref();
const fullName = routeUtils.getFullTitle();

const {
  tableId,
  queryParams,
  loading,
  columns,
  rowStyle,
  dataList,
  pagination,
  onSearch,
  resetForm,
  openDialog,
  handleDelete,
  handleSizeChange,
  handleCurrentChange,
  handleSelectionChange
} = hook();

const queryOptions = reactive({} as Options);
const { initData } = extend(queryParams, queryOptions);
initData();
</script>

<template>
  <div class="main">
    <el-form
      ref="formRef"
      :inline="true"
      :model="queryParams"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
      label-width="auto"
    >
      <!-- 服务商 -->
      <el-form-item label="服务商" prop="fIspID">
        <el-select
          v-model="queryParams.fIspID"
          placeholder="全部"
          filterable
          clearable
          style="width: 200px"
          :disabled="!queryOptions.ispList?.length"
        >
          <el-option
            v-for="item in queryOptions.ispList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- 客户 -->
      <el-form-item label="客户" prop="fCusID">
        <el-select
          v-model="queryParams.fCusID"
          placeholder="全部"
          filterable
          clearable
          style="width: 200px"
          :disabled="!queryOptions.cusList?.length"
        >
          <el-option
            v-for="item in queryOptions.cusList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- 关键字 -->
      <el-form-item label="关键字" prop="searchValue">
        <el-input
          v-model="queryParams.searchValue"
          placeholder=""
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri/search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button
          :icon="useRenderIcon('ep/refresh')"
          @click="resetForm(formRef)"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <PureTableBar :title="fullName" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          v-pers="Pers.NEW"
          type="primary"
          :icon="useRenderIcon('ri/add-circle-line')"
          @click="openDialog()"
        >
          新增
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          border
          align-whole="left"
          showOverflowTooltip
          table-layout="auto"
          :loading="loading"
          :size="size"
          adaptive
          :row-style="rowStyle"
          :adaptiveConfig="{ offsetBottom: 108 }"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          @selection-change="handleSelectionChange"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <!-- 查看 -->
          <template #operation="{ row }">
            <el-button
              v-pers="Pers.SHOW"
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep/view')"
              @click="openDialog('查看', row[tableId])"
            >
              查看
            </el-button>

            <!-- 修改 -->
            <el-button
              v-pers="Pers.EDIT"
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep/edit-pen')"
              @click="openDialog('修改', row[tableId])"
            >
              修改
            </el-button>

            <!-- 删除 -->
            <el-popconfirm
              :title="`是否确认删除`"
              @confirm="handleDelete(row[tableId])"
            >
              <template #reference>
                <el-button
                  v-pers="Pers.DEL"
                  class="reset-margin"
                  link
                  type="primary"
                  :size="size"
                  :icon="useRenderIcon('ep/delete')"
                >
                  删除
                </el-button>
              </template>
              <template #actions="{ confirm, cancel }">
                <el-button type="danger" size="small" @click="confirm">
                  确认
                </el-button>
                <el-button size="small" @click="cancel">取消</el-button>
              </template>
            </el-popconfirm>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-dropdown-menu__item i) {
  margin: 0;
}

.main-content {
  margin: 24px 24px 0 !important;
}

.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
