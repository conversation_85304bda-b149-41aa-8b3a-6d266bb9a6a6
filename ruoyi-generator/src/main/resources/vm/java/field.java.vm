#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
#if($column.javaField.length() > 2 && $column.javaField.substring(1,2).matches("[A-Z]"))
#set($AttrName=$column.javaField)
#else
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
#end
#set($comment=$column.columnComment)
#if($column.javaType == "String")
#set($defaultValue='""')
#else
#set($defaultValue="0L")
#end
${className}.set${AttrName}(${defaultValue}); // ${comment}
#end
#end
