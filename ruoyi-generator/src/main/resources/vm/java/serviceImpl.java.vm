package ${packageName}.${moduleName}.service.impl;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rj.rjcloudapi.cloud.entity.Cus;
import com.rj.rjcloudapi.cloud.mapper.CusMapper;
import com.rj.rjcloudapi.cloud.service.ISyncService;
import com.rj.rjcloudapi.common.utils.CheckUtils;
import com.rj.rjcloudapi.common.utils.SnowFlakeUtil;
import com.rj.rjcloudapi.common.utils.date.DateUtils;
import com.rj.rjcloudapi.core.utils.SecurityUtils;
import ${packageName}.${moduleName}.entity.${ClassName};
import ${packageName}.${moduleName}.mapper.${ClassName}Mapper;
import ${packageName}.${moduleName}.service.I${ClassName}Service;

import lombok.extern.slf4j.Slf4j;

/**
 * ${functionName}Service业务层处理
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Slf4j
@Service
public class ${ClassName}ServiceImpl extends ServiceImpl<${ClassName}Mapper, ${ClassName}> implements I${ClassName}Service {
    @Autowired
    private ${ClassName}Mapper ${className}Mapper;
    @Autowired
    private ISyncService syncService;
    @Autowired
    private CusMapper cusMapper;

    /**
     * 查询${functionName}
     *
     * @param ${pkColumn.javaField} ${functionName}主键
     * @return ${functionName}
     */
    @Override
    public ${ClassName} select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField}) {
        ${ClassName} ${className} = check${ClassName}(${pkColumn.javaField});
        return ${className};
    }

    /**
     * 检查${functionName}是否存在
     *
     * @param ${pkColumn.javaField} ${functionName}主键
     * @return ${functionName}
     */
    private ${ClassName} check${ClassName}(${pkColumn.javaType} ${pkColumn.javaField}) {
        ${ClassName} ${className} = ${className}Mapper.select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField});
        Assert.notNull(${className}, "${functionName}不存在");
        return ${className};
    }

    /**
     * 查询${functionName}列表
     *
     * @param ${className} ${functionName}
     * @return ${functionName}
     */
    @Override
    public List<${ClassName}> select${ClassName}List(${ClassName} ${className}) {
        List<${ClassName}> list = ${className}Mapper.select${ClassName}List(${className});
        return list;
    }

    /**
     * 新增${functionName}
     *
     * @param ${className} ${functionName}
     * @return 结果
     */
#if($table.sub)
@Transactional
#end
    @Override
    public ${ClassName} insert${ClassName}(${ClassName} ${className}) {
#foreach ($column in $columns)
    #if($column.javaField == 'fPlatRowID')
        ${className}.setfPlatRowID(SnowFlakeUtil.nextId());
    #end
    #if($column.javaField == 'fCreTime')
        ${className}.setfCreTime(DateUtils.getCurrentTimeStamp());
    #end
    #if($column.javaField == 'fCreUsrID')
        ${className}.setfCreUsrID(SecurityUtils.getfUsrID());
    #end
    #if($column.javaField == 'fCreUsrNo')
        ${className}.setfCreUsrNo(SecurityUtils.getfUsrNo());
    #end
    #if($column.javaField == 'fCreUsrName')
        ${className}.setfCreUsrName(SecurityUtils.getfUsrName());
    #end
    #if($column.javaField == 'fRemark')
        ${className}.setfRemark(Optional.ofNullable(${className}.getfRemark()).orElse(""));
    #end
#end

#if($table.sub)
        int rows = ${className}Mapper.insert${ClassName}(${className});
        insert${subClassName}(${className});
        return rows;
#else
        check${ClassName}(${className});
        handle${ClassName}(${className});
        ${className}Mapper.insert${ClassName}(${className});
        return ${className};
#end
    }

    private ${ClassName} handle${ClassName}(${ClassName} ${className}) {
#foreach ($column in $columns)
    #if($column.javaField == 'fCusID')
    #set($hasCus=true)
    #end
#end
        if (CheckUtils.isGtZero(${className}.getfCusID())) {
            Cus cus = cusMapper.selectCusByFCusID(${className}.getfCusID());
            Assert.notNull(cus, "客户不存在");
    #foreach ($column in $columns)
        #if($column.javaField == 'fIspID' && $hasCus)
            ${className}.setfIspID(cus.getfIspID());
        #end
        #if($column.javaField == 'fIspName' && $hasCus)
            ${className}.setfIspName(cus.getfIspName());
        #end
        #if($column.javaField == 'fCusNo' && $hasCus)
            ${className}.setfCusNo(cus.getfCusNo());
        #end
        #if($column.javaField == 'fCusName' && $hasCus)
            ${className}.setfCusName(cus.getfCusName());
        #end
    #end
        }
#foreach ($column in $columns)
    #if($column.javaField == 'fOpLastTime')
        ${className}.setfOpLastTime(DateUtils.getCurrentTimeStamp());
    #end
    #if($column.javaField == 'fSyncTime')
        ${className}.setfSyncTime(0L);
    #end
    #if($column.javaField == 'fSyncRemark')
        ${className}.setfSyncRemark("");
    #end
    #if($column.javaField == 'fOthSyncTime')
        ${className}.setfOthSyncTime(0L);
    #end
#end
        return ${className};
    }

    private void check${ClassName}(${ClassName} ${className}) {

    }

    /**
     * 修改${functionName}
     *
     * @param ${className} ${functionName}
     * @return 结果
     */
        #if($table.sub)
        @Transactional
        #end
    @Override
    public ${ClassName} update${ClassName}(${ClassName} ${className}) {
        check${ClassName}(${className}.get${pkColumn.javaField}());
        #if($table.sub)
                ${className}Mapper.delete${subClassName}By${subTableFkClassName}(${className}.get${pkColumn.capJavaField}());
            insert${subClassName}(${className});
        #end
        check${ClassName}(${className});
        handle${ClassName}(${className});
        ${className}Mapper.update${ClassName}(${className});
        return ${className};
    }

    /**
     * 删除${functionName}信息
     *
     * @param ${pkColumn.javaField} ${functionName}主键
     * @return 结果
     */
        #if($table.sub)
        @Transactional
        #end
    @Override
    @Transactional
    public ${ClassName} delete${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField}) {
#if($table.sub)
        ${className}Mapper.delete${subClassName}By${subTableFkClassName}(${pkColumn.javaField});
#end
        ${ClassName} ${className} = check${ClassName}(${pkColumn.javaField});
        syncService.insertRemoveData(${className});
        ${className}Mapper.delete${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField});
        return ${className};
    }
#if($table.sub)

    /**
     * 新增${subTable.functionName}信息
     *
     * @param ${className} ${functionName}对象
     */
    public ${ClassName} insert${subClassName}(${ClassName} ${className}) {
        List<${subClassName}> ${subclassName}List = ${className}.get${subClassName}List();
        ${pkColumn.javaType} ${pkColumn.javaField} = ${className}.get${pkColumn.capJavaField}();
        if (StringUtils.isNotNull(${subclassName}List))
        {
            List<${subClassName}> list = new ArrayList<${subClassName}>();
            for (${subClassName} ${subclassName} : ${subclassName}List)
            {
                ${subclassName}.set${subTableFkClassName}(${pkColumn.javaField});
                list.add(${subclassName});
            }
            if (list.size() > 0)
            {
                    ${className}Mapper.batch${subClassName}(list);
            }
        }
    }
#end
}
