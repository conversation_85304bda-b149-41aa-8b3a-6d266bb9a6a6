package ${packageName}.${moduleName}.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rj.rjcloudapi.common.annotation.OpLog;
import com.rj.rjcloudapi.common.utils.AjaxResult;
import com.rj.rjcloudapi.common.utils.TableDataInfo;
import com.rj.rjcloudapi.core.BaseController;
import ${packageName}.${moduleName}.entity.${ClassName};
import ${packageName}.${moduleName}.service.I${ClassName}Service;

/**
 * ${functionName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@OpLog(entity = ${ClassName}.class)
@RestController
@RequestMapping("/${moduleName}/${businessName}")
public class ${ClassName}Controller extends BaseController {
    @Autowired
    private I${ClassName}Service ${className}Service;
##    /**
##     * 查询${functionName}所有列表
##     */
##    @PostMapping("/allList")
##    public AjaxResult allList(${ClassName} ${className}) {
##        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
##        return success(list);
##    }

    /**
     * 查询${functionName}列表
     */
    @PostMapping("/list")
#if($table.crud || $table.sub)
    public TableDataInfo list(${ClassName} ${className}) {
        startPage();
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        return getDataTable(list);
    }
#elseif($table.tree)
    public AjaxResult list(${ClassName} ${className}) {
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        return success(list);
    }
#end

##    /**
##     * 导出${functionName}列表
##     */
##    @Log(title = "${functionName}", businessType = BusinessType.EXPORT)
##    @PostMapping("/export")
##    public void export(HttpServletResponse response, ${ClassName} ${className}) {
##        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
##        ExcelUtil<${ClassName}> util = new ExcelUtil<${ClassName}>(${ClassName}.class);
##        util.exportExcel(response, list, "${functionName}数据");
##    }

    /**
     * 获取${functionName}详细信息
     */
    @PostMapping("/info/{${pkColumn.javaField}}")
    public AjaxResult info(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField}) {
        return success(${className}Service.select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField}));
    }

    /**
     * 新增${functionName}
     */
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody ${ClassName} ${className}) {
        return success(${className}Service.insert${ClassName}(${className}));
    }

    /**
     * 修改${functionName}
     */
    @PostMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody ${ClassName} ${className}) {
        return success(${className}Service.update${ClassName}(${className}));
    }

    /**
     * 删除${functionName}
     */
    @PostMapping("/remove/{${pkColumn.javaField}}")
    public AjaxResult remove(@PathVariable ${pkColumn.javaType} ${pkColumn.javaField}) {
        return success(${className}Service.delete${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField}));
    }
}
